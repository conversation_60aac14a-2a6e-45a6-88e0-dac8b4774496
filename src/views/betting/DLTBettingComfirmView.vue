<template>
    <div class="betting-confirm-container">
        <!-- 顶部导航 -->
        <TopNavigation title="大乐透 选号列表">
            <template #right-buttons>
                <button class="clear-btn" @click="clearAll">清空</button>
            </template>
        </TopNavigation>

        <!-- 期号和截止时间 -->
        <div class="deadline-info">
            <van-icon name="lock" class="lock-icon" />
            <span class="issue-info">{{ currentPeriod }} {{ deadlineTime }} 截止，请尽快提交投注</span>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <van-button type="default" class="action-btn continue-select" size="small" @click="continueSelect">
                <span class="btn-icon">+</span>继续选号
            </van-button>
            <van-button type="danger" class="action-btn machine-select" size="small" @click="randomSelect">
                机选一注
            </van-button>
        </div>

        <!-- 已选号码列表 -->
        <div class="selected-numbers-list">
            <div v-for="(section, index) in betSections" :key="index" class="number-card">
                <!-- 展示号码 -->
                <div class="numbers-container">
                    <!-- 渲染前区号码 -->
                    <div class="front-numbers">
                        <template v-if="section.bet_type === 'normal'">
                            <div v-for="num in section.content.front" :key="'front-' + num" class="number-ball red">
                                {{ num < 10 ? '0' + num : num }} </div>
                        </template>
                        <template v-else>
                            <!-- 胆拖投注展示 -->
                            <div v-for="num in section.content.front_dan" :key="'dan-' + num" class="number-ball red">
                                {{ num < 10 ? '0' + num : num }} </div>
                                    <span class="number-separator"
                                        v-if="section.content.front_dan.length && section.content.front_tuo.length">拖</span>
                                    <div v-for="num in section.content.front_tuo" :key="'tuo-' + num"
                                        class="number-ball red">
                                        {{ num < 10 ? '0' + num : num }} </div>
                        </template>
                    </div>

                    <!-- 渲染后区号码 -->
                    <div class="back-numbers">
                        <template v-if="section.bet_type === 'normal'">
                            <div v-for="num in section.content.back" :key="'back-' + num" class="number-ball blue">
                                {{ num < 10 ? '0' + num : num }} </div>
                        </template>
                        <template v-else>
                            <!-- 胆拖投注展示 -->
                            <div v-for="num in section.content.back_dan" :key="'back-dan-' + num"
                                class="number-ball blue">
                                {{ num < 10 ? '0' + num : num }} </div>
                                    <span class="number-separator"
                                        v-if="section.content.back_dan.length && section.content.back_tuo.length">拖</span>
                                    <div v-for="num in section.content.back_tuo" :key="'back-tuo-' + num"
                                        class="number-ball blue">
                                        {{ num < 10 ? '0' + num : num }} </div>
                        </template>
                    </div>
                </div>

                <!-- 投注信息 -->
                <div class="bet-info">
                    <div class="bet-type">
                        {{ getBetTypeText(section) }}
                    </div>

                    <!-- 投注倍数选择 -->
                    <div class="multiple-selector">
                        <van-stepper v-model="section.multiple" min="1" integer button-size="28px" input-width="40px" />
                    </div>
                </div>

                <!-- 删除按钮 -->
                <van-icon name="cross" class="delete-btn" @click="removeSection(index)" />
            </div>
        </div>

        <!-- 追加和追号选项 -->
        <div class="extra-options">
            <!-- 追加投注选项 -->
            <div class="option-item">
                <van-checkbox v-model="addExtra" @change="onAddExtraChange">
                    <span class="option-label">追加投注</span>
                    <span class="option-desc">(最高可中1800万)</span>
                </van-checkbox>
                <div class="price-info">
                    <span class="current-price">{{ unitPrice }}元/注</span>
                    <span v-if="!addExtra" class="price-desc">选择追加后3元/注</span>
                </div>
            </div>

            <!-- 追号选项 -->
            <div class="option-item">
                <div class="option-header">
                    <span class="option-label">追号投注</span>
                    <span class="periods-display" @click="showPeriodSelector = true">
                        {{ periods }}期 <van-icon name="arrow-down" />
                    </span>
                </div>
                <div class="period-quick-select">
                    <div
                        v-for="period in quickPeriods"
                        :key="period"
                        class="period-btn"
                        :class="{ active: periods === period }"
                        @click="selectPeriod(period)"
                    >
                        {{ period }}期
                    </div>
                </div>
            </div>
        </div>

        <!-- 期数选择弹窗 -->
        <van-popup v-model:show="showPeriodSelector" position="bottom" round>
            <div class="period-selector-popup">
                <div class="popup-header">
                    <span class="popup-title">选择追号期数</span>
                    <van-icon name="cross" @click="showPeriodSelector = false" />
                </div>
                <div class="period-grid">
                    <div
                        v-for="period in allPeriods"
                        :key="period"
                        class="period-grid-item"
                        :class="{ active: periods === period }"
                        @click="selectPeriod(period)"
                    >
                        {{ period }}
                    </div>
                </div>
                <div class="popup-footer">
                    <van-button type="primary" block @click="showPeriodSelector = false">
                        确定
                    </van-button>
                </div>
            </div>
        </van-popup>

        <!-- 底部操作区 -->
        <div class="bottom-actions">
            <div class="buttons-group">
                <van-button plain hairline type="danger" class="action-button" size="small" @click="purchaseBet">合买</van-button>
                <van-button type="default" size="small" class="action-button">保存</van-button>
            </div>
            <div class="total-info">
                {{ totalBetCount }}注 {{ totalPeriods }}期
                <div class="total-amount">{{ totalAmount }}元</div>
            </div>
            <van-button 
                type="danger" 
                size="small"
                class="confirm-btn" 
                @click="confirmBet(false)" 
                :loading="isSubmitting"
                loading-text="提交中..."
                :disabled="isSubmitting || betSections.length === 0"
            >
                确认投注
            </van-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onActivated, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDLTStore } from '@/stores/lottery/dlt'
import TopNavigation from '@/components/ui/TopNavigation.vue'
import betApi from '@/api/bet'
import {
    showToast,
    Button as VanButton,
    Icon as VanIcon,
    Checkbox as VanCheckbox,
    Stepper as VanStepper,
    Popup as VanPopup
} from 'vant'

const router = useRouter()
const dltStore = useDLTStore()

// 期号和截止时间
const currentPeriod = computed(() => {
    return dltStore.currentPeriod || '2025-05-03'
})

const deadlineTime = computed(() => {
    // 如果有endTime则格式化，否则使用默认值
    if (dltStore.endTime) {
        const timeStr = dltStore.endTime.split(' ')[1] || '20:50:00'
        return timeStr
    }
    return '20:50:00'
})

// 投注数据 - 使用计算属性实时获取最新数据
const periods = ref(1)
const addExtra = ref(false)
const isSubmitting = ref(false) // 添加提交状态变量

// 追号相关数据
const showPeriodSelector = ref(false)
const quickPeriods = [1, 2, 5, 10, 20]
const allPeriods = Array.from({ length: 100 }, (_, i) => i + 1)

// 投注倍数相关
const increaseMultiple = (index) => {
    if (!betSections.value[index].multiple) {
        betSections.value[index].multiple = 1
    }
    betSections.value[index].multiple++
}

const decreaseMultiple = (index) => {
    if (betSections.value[index].multiple > 1) {
        betSections.value[index].multiple--
    }
}

// 移除某个投注
const removeSection = (index) => {
    dltStore.removeSelection(index)
}

// 清空所有
const clearAll = () => {
    dltStore.clearAllSelections()
}

// 追加投注变化处理
const onAddExtraChange = (value) => {
    console.log('追加投注状态变化:', value)
}

// 选择期数
const selectPeriod = (period) => {
    periods.value = period
    showPeriodSelector.value = false
    console.log('选择期数:', period)
}

// 继续选号
const continueSelect = () => {
    // 保存当前确认页的倍数设置回dltStore
    if (betSections.value.length > 0) {
        // 不清空dltStore的betSections，只更新倍数属性
        // 这样返回选号页面时，已有选号不会丢失
        dltStore.betSections.forEach((section, index) => {
            if (betSections.value[index]) {
                section.multiple = betSections.value[index].multiple || 1;
            }
        });
    }
    
    router.back();
}

// 机选一注
const randomSelect = () => {
    // 生成一注随机号码
    dltStore.randomAll(1)

    // 添加倍数属性到新生成的投注记录
    const latestSections = dltStore.betSections
    latestSections.forEach(section => {
        if (!section.multiple) {
            section.multiple = 1
        }
    })
}

// 获取投注类型文本
const getBetTypeText = (section) => {
    // 计算注数 - 使用section自身的计算结果
    let betCount = 1; // 默认1注
    
    // 胆拖和复式需要计算组合数
    if (section.bet_type === 'dan_tuo') {
        // 计算前区组合数
        const frontDan = section.content.front_dan.length;
        const frontTuo = section.content.front_tuo.length;
        const frontNeeded = 5 - frontDan;
        const frontComb = calculateCombination(frontTuo, frontNeeded);
        
        // 计算后区组合数
        const backDan = section.content.back_dan.length;
        const backTuo = section.content.back_tuo.length;
        const backNeeded = 2 - backDan;
        const backComb = calculateCombination(backTuo, backNeeded);
        
        // 总注数 = 前区组合数 * 后区组合数
        betCount = frontComb * backComb;
    } else if (section.isMultiple) {
        // 复式投注
        const frontCount = section.content.front.length;
        const backCount = section.content.back.length;
        
        // 计算前区组合数 C(n,5)
        const frontComb = calculateCombination(frontCount, 5);
        
        // 计算后区组合数 C(n,2)
        const backComb = calculateCombination(backCount, 2);
        
        betCount = frontComb * backComb;
    }
    
    // 保存实际注数到bet_count字段，不影响multiple字段
    section.bet_count = betCount;
    
    const multiple = section.multiple || 1; // 倍数
    const currentUnitPrice = unitPrice.value; // 当前单价（根据是否追加决定）

    if (section.bet_type === 'dan_tuo') {
        return `[胆拖] ${betCount}注 ${multiple}倍 ${betCount * currentUnitPrice * multiple}元`
    } else if (section.isMultiple) {
        return `[复式] ${betCount}注 ${multiple}倍 ${betCount * currentUnitPrice * multiple}元`
    } else {
        return `[单式] 1注 ${multiple}倍 ${currentUnitPrice * multiple}元`
    }
}

// 计算组合数 C(n,m)
function calculateCombination(n, m) {
    if (m > n) return 0
    if (m === 0 || m === n) return 1
    
    // 使用公式 C(n,m) = n! / (m! * (n-m)!)
    // 为避免大数溢出，通过递推公式计算: C(n,m) = C(n,m-1) * (n-m+1) / m
    let result = 1
    for (let i = 1; i <= m; i++) {
        result = result * (n - i + 1) / i
    }
    return Math.round(result)
}

// 计算总投注数和总金额
const totalBetCount = computed(() => {
    let count = 0
    betSections.value.forEach(section => {
        // 确保每个section的bet_count是最新计算的
        getBetTypeText(section);
        // 总注数 = 每个选号的注数 × 倍数
        count += section.bet_count * (section.multiple || 1);
    })
    return count
})

const totalPeriods = computed(() => {
    return periods.value
})

// 计算单注价格
const unitPrice = computed(() => {
    return addExtra.value ? 3 : 2 // 追加：3元/注，不追加：2元/注
})

// 计算总金额
const totalAmount = computed(() => {
    let amount = 0
    betSections.value.forEach(section => {
        // 确保每个section的bet_count是最新计算的
        getBetTypeText(section);
        // 总金额 = 注数 × 倍数 × 期数 × 单价
        amount += section.bet_count * (section.multiple || 1) * periods.value * unitPrice.value;
    })
    return amount
})

// 确认投注
const confirmBet = async (arg = false) => {
    // 检查参数类型，如果是事件对象，则忽略它
    const isShare = typeof arg === 'boolean' ? arg : false;
    
    console.log('confirmBet函数调用，参数类型:', typeof arg);
    console.log('使用的isShare值:', isShare);

    // 检查是否有选号
    if (betSections.value.length === 0) {
        showToast('请至少选择一注');
        return null;
    }

    // 检查投注金额是否大于0
    if (totalAmount.value <= 0) {
        showToast('投注金额必须大于0');
        return null;
    }

    // 检查投注金额是否超过限制
    const MAX_BET_AMOUNT = 100000; // 最大投注金额限制
    if (totalAmount.value > MAX_BET_AMOUNT) {
        showToast(`投注金额不能超过${MAX_BET_AMOUNT}元`);
        return null;
    }

    // 设置提交状态
    isSubmitting.value = true;

    try {
        // 先更新所有section的注数
        betSections.value.forEach(section => {
            getBetTypeText(section);
        });
        
        // 构建API请求格式
        const apiData = {
            bet_data: betSections.value.map(section => {
                // 创建一个新对象，确保不会影响原始对象
                const betData = { ...section };

                // 确保multiple字段正确设置 - 这是用户在界面上选择的倍数
                betData.bet_multiple = section.multiple || 1;

                // 确保bet_count字段正确 - 计算得到的注数
                betData.bet_count = section.bet_count;

                // 打印当前投注项的详细信息
                console.log(`投注项: 类型=${betData.bet_type}, 注数=${betData.bet_count}, 倍数=${betData.bet_multiple}`);

                return betData;
            }),
            lottery_type: 'dlt',
            issue_number: currentPeriod.value,
            issue_id: dltStore.issueId || 1, // 使用dltStore中存储的实际issueId
            union: isShare ? 1 : 0, // 如果是合买操作，添加union参数为1
            add_extra: addExtra.value ? 1 : 0, // 追加投注
            periods: periods.value // 追号期数
        };

        // 打印投注明细
        console.log('投注数据:', apiData);

        // 调用API提交投注
        const result = await betApi.createDLTBet(apiData);

        // 更详细地打印API响应结果
        console.log('投注API完整响应:', JSON.stringify(result));
        console.log('投注API响应code:', result.code);
        console.log('投注API响应data:', result.data);
        console.log('isShare参数值:', isShare);

        if (result.code === 1 || result.code === 200) {
            console.log('响应状态码检查通过');
            // 如果是发单或合买操作，不显示提示，直接返回结果
            if (!isShare) {
                console.log('正常投注分支执行');
                showToast('投注成功');
                // 投注成功后才清空所有选号
                dltStore.clearAllSelections();
                // 同时清空选号页面上当前正在选择的号码
                dltStore.clearSelectedNumbers();
                
                // 获取订单ID并跳转到支付确认页面
                if (result.data && result.data.order_id) {
                    console.log('获取到订单ID，准备跳转到支付确认页面:', result.data.order_id);
                    try {
                        await router.push({
                            path: '/pay/confirm',
                            query: { order_id: result.data.order_id }
                        });
                        console.log('跳转到支付确认页面完成');
                    } catch (routeError) {
                        console.error('路由跳转失败:', routeError);
                        // 如果路由不存在，尝试其他可能的路径
                        router.push('/bet-records');
                    }
                } else {
                    console.warn('没有获取到订单ID，跳转到订单列表页');
                    // 如果没有获取到订单ID，则跳转到订单列表
                    router.push('/bet-records');
                }
            }
            return result;
        } else {
            showToast(result.msg || '投注失败，请重试');
            return null;
        }
    } catch (error) {
        console.error('投注请求失败:', error);
        showToast('投注失败，请检查网络连接');
        return null;
    } finally {
        // 无论成功失败，都重置提交状态，除非是发单或合买操作
        if (!isShare) {
            isSubmitting.value = false;
        }
    }
}

// 添加合买功能
const purchaseBet = async () => {
    console.log('开始合买操作');
    
    // 检查投注金额是否大于0
    if (totalAmount.value <= 0) {
        showToast('投注金额必须大于0');
        return;
    }
    
    // 设置提交状态，避免重复点击
    isSubmitting.value = true;
    
    try {
        // 提交订单获取order_id
        const result = await confirmBet(true); // 传入true表示是合买操作
        
        console.log('合买订单创建结果:', result);
        
        if (result && (result.code === 1 || result.code === 200) && result.data && result.data.order_id) {
            console.log('合买订单创建成功，订单ID:', result.data.order_id);
            
            // 计算总金额 - 使用简单数值而非响应式对象
            const amount = Number(totalAmount.value);
            
            // 构建跳转参数
            const query = {
                order_id: String(result.data.order_id),
                amount: String(amount),
                type: 'dlt'
            };
            
            console.log('准备跳转到合买页面，参数:', query);
            
            // 成功获取order_id后跳转到合买创建页面
            router.push({
                path: '/union/create',
                query
            });
        } else {
            console.error('订单创建失败或返回数据格式不正确:', result);
            if (result && result.msg) {
                showToast(result.msg);
            } else {
                showToast('创建订单失败，请重试');
            }
            isSubmitting.value = false;
        }
    } catch (error) {
        console.error('合买操作异常:', error);
        showToast('系统异常，请稍后重试');
        isSubmitting.value = false;
    }
}

// 使用计算属性实时获取最新投注数据，解决keepAlive缓存问题
const betSections = computed(() => {
    // 强制访问响应式属性以确保依赖追踪
    const storeBetSections = dltStore.betSections;
    const sectionsLength = storeBetSections.length;

    console.log('🔍 计算属性：获取最新投注数据');
    console.log('🔍 dltStore.betSections 长度:', sectionsLength);
    console.log('🔍 dltStore.betSections 详细内容:', JSON.stringify(storeBetSections, null, 2));
    console.log('🔍 dltStore.betSections 引用:', storeBetSections);

    if (storeBetSections && sectionsLength > 0) {
        // 直接使用store中的最新数据，添加倍数属性
        const sections = storeBetSections.map((section, index) => {
            console.log(`🔍 处理第${index + 1}注:`, JSON.stringify(section, null, 2));

            // 创建一个完整的副本，添加倍数属性
            const newSection = JSON.parse(JSON.stringify(section));
            newSection.multiple = 1;

            // 计算注数信息
            getBetTypeText(newSection);

            console.log(`🔍 处理后第${index + 1}注:`, JSON.stringify(newSection, null, 2));
            return newSection;
        });

        console.log('✅ 计算属性：返回最新投注数据，sections 长度:', sections.length);
        return sections;
    } else {
        console.log('❌ 计算属性：没有投注数据，返回空数组');
        return [];
    }
})

// 初始化页面 - 使用计算属性后无需手动同步数据
onMounted(async () => {
    console.log('确认页面 onMounted 执行');

    // 如果当前没有期号信息，先获取期号
    if (!dltStore.currentPeriod) {
        await dltStore.getCurrentPeriod();
    }

    console.log('确认页面初始化完成，使用计算属性自动获取最新投注数据');
})

// 页面激活时执行（处理keepAlive缓存的情况）
onActivated(() => {
    console.log('确认页面 onActivated 执行 - 页面被激活');
    console.log('当前betSections数据:', dltStore.betSections);
    console.log('计算属性会自动获取最新数据，无需手动同步');
})

// 监听 dltStore.betSections 的变化
watch(() => dltStore.betSections, (newValue, oldValue) => {
    console.log('🔍 Watch: dltStore.betSections 发生变化');
    console.log('🔍 Watch: 旧值长度:', oldValue?.length || 0);
    console.log('🔍 Watch: 新值长度:', newValue?.length || 0);
    console.log('🔍 Watch: 新值内容:', JSON.stringify(newValue, null, 2));
}, { deep: true, immediate: true })
</script>

<style scoped>
.betting-confirm-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 90px; /* 为底部固定操作栏预留足够空间 (60px高度 + 30px缓冲) */
}

.clear-btn {
    color: white;
    font-size: 14px;
    background: none;
    border: none;
}

.deadline-info {
    background-color: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    color: #e64545;
    font-size: 14px;
    border-bottom: 1px solid #eee;
}

.lock-icon {
    margin-right: 6px;
    color: #e64545;
}

.action-buttons {
    display: flex;
    padding: 12px;
    gap: 12px;
}

.action-btn {
    flex: 1;
    height: 36px;
    border-radius: 4px;
}

.continue-select {
    color: #e64545;
    border: 1px solid #e64545;
}

.machine-select {
    background-color: #e64545;
    color: white;
}

.btn-icon {
    margin-right: 2px;
    font-weight: bold;
}

.selected-numbers-list {
    padding: 0 12px;
    margin-bottom: 16px; /* 与追加选项区域保持适当间距 */
}

.number-card {
    background-color: white;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 12px;
    position: relative;
}

.numbers-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.front-numbers,
.back-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.number-ball {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: white;
    font-size: 13px;
}

.red {
    background-color: #e64545;
}

.blue {
    background-color: #3e7dff;
}

.number-separator {
    display: flex;
    align-items: center;
    margin: 0 4px;
    color: #666;
}

.bet-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    border-top: 1px solid #eee;
    padding-top: 12px;
}

.bet-type {
    font-size: 14px;
    color: #666;
}

.delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 16px;
    background-color: #f5f5f5;
    border-radius: 50%;
}

.extra-options {
    background-color: white;
    padding: 16px;
    border-radius: 8px;
    margin: 0 12px 20px; /* 增加底部margin，确保与底部操作栏有足够间距 */
}

.option-item {
    margin-bottom: 16px;
}

.option-item:last-child {
    margin-bottom: 0;
}

.option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.option-label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.option-desc {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.price-info {
    margin-top: 8px;
    margin-left: 24px;
}

.current-price {
    font-size: 14px;
    color: #e64545;
    font-weight: 500;
}

.price-desc {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
}

.periods-display {
    display: flex;
    align-items: center;
    color: #e64545;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.periods-display .van-icon {
    margin-left: 4px;
    font-size: 12px;
}

.period-quick-select {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.period-btn {
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
    outline: none; /* 移除默认焦点轮廓 */
    -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
}

.period-btn.active {
    background-color: #e64545 !important;
    color: #ffffff !important;
    border-color: #e64545 !important;
}

.period-btn:hover:not(.active) {
    border-color: #e64545;
    color: #e64545;
    background: white;
}

.period-btn:focus {
    outline: none;
    background: white;
}

.period-btn:focus:not(.active) {
    border-color: #e64545;
    color: #e64545;
    background: white;
}

.period-btn:active:not(.active) {
    background: #f5f5f5;
    border-color: #e64545;
    color: #e64545;
}

.period-selector-popup {
    padding: 20px;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}

.popup-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.popup-header .van-icon {
    font-size: 18px;
    color: #999;
    cursor: pointer;
}

.period-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.period-grid-item {
    padding: 12px;
    text-align: center;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
}

.period-grid-item.active {
    background-color: #e64545 !important;
    color: #ffffff !important;
    border-color: #e64545 !important;
}

.period-grid-item:hover:not(.active) {
    border-color: #e64545;
    color: #e64545;
}

.popup-footer {
    padding-top: 12px;
    border-top: 1px solid #eee;
}

.bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    padding: 12px 16px;
    align-items: center;
    border-top: 1px solid #eee;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100; /* 确保底部操作栏在最上层 */
    min-height: 60px; /* 设置最小高度，确保内容不会被压缩 */
}

.buttons-group {
    display: flex;
    gap: 8px;
}

.action-button {
    min-width: 54px;
    height: 36px;
    font-size: 14px;
}

.total-info {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #666;
    margin: 0 10px;
}

.total-amount {
    font-size: 18px;
    color: #e64545;
    font-weight: 500;
}

.confirm-btn {
    min-width: 80px;
    height: 36px;
}

.confirm-btn:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 375px) {
    .betting-confirm-container {
        padding-bottom: 100px; /* 小屏幕设备增加更多底部空间 */
    }

    .extra-options {
        margin: 0 8px 20px; /* 小屏幕减少左右边距 */
        padding: 12px; /* 减少内边距 */
    }

    .bottom-actions {
        padding: 10px 12px; /* 小屏幕减少内边距 */
    }

    .action-button {
        min-width: 48px; /* 减少按钮最小宽度 */
        font-size: 13px; /* 减少字体大小 */
    }
}

@media (min-width: 768px) {
    .betting-confirm-container {
        max-width: 500px; /* 平板设备限制最大宽度 */
        margin: 0 auto; /* 居中显示 */
    }
}
</style>
